import { PaymentProviderType } from '@/api/client/donation/create-payment.dto'
import { IPaymentProvider } from '@/api/client/donation/donation.service'
import { StripeService } from '@/api/client/donation/payment-providers/stripe.service'
import { YookassaService } from '@/api/client/donation/payment-providers/yookassa.service'
import { Subscriptions } from '@/common/subscription/subscription.constants'
import { LibraryTranslation } from '@/entity/LibraryTranslation'
import { User } from '@/entity/User'
import { UserSubscriptions } from '@/entity/UserSubscriptions'
import { BadRequestException, Injectable } from '@nestjs/common'

@Injectable()
export class SubscriptionService {
  private providers: Map<PaymentProviderType, IPaymentProvider> = new Map();

  constructor(
    private readonly yookassaService: YookassaService,
    private readonly stripeService: StripeService,
  ) {
    this.providers.set(PaymentProviderType.YOOKASSA, this.yookassaService);
    this.providers.set(PaymentProviderType.STRIPE, this.stripeService);
  }

  async getList() {
    return Subscriptions;
  }

  async pay(body: any, user: User) {
    const subscription = Subscriptions[body.type];
    const payment = body.payment;
    const autoRenew: boolean = true;
    const isYearly: boolean = body.isYearly || false;

    if (!subscription) {
      throw new BadRequestException('Подписка не найдена');
    }

    const provider = this.providers.get(payment);
    if (!provider) {
      throw new BadRequestException('Выбранный способ оплаты не поддерживается.');
    }

    let currency: string;
    if (payment === PaymentProviderType.STRIPE) currency = 'EUR';
    else if (payment === PaymentProviderType.YOOKASSA) currency = 'RUB';
    else throw new BadRequestException('Валюта не поддерживается.');

    const period = isYearly ? 'yearly' : 'monthly';
    const price = payment === 'stripe' ? subscription.price[period].eur : subscription.price[period].rub;
    const description = `Оплата подписки ${subscription.name} (${isYearly ? 'годовая' : 'месячная'}), пользователь ${user.firstName} ${user.lastName}`;

    const metadata = {
      module: 'subscriptions',
      value: body.type,
      userId: user.id,
      autoRenew,
      isYearly,
    };

    if (payment === PaymentProviderType.STRIPE && autoRenew) {
      const stripePriceId = subscription.stripePriceId[period];
      if (!stripePriceId) {
        throw new BadRequestException('Stripe priceId не настроен для подписки');
      }
      return (this.stripeService as any).createSubscriptionCheckout(
        stripePriceId,
        user.email,
        description + ` (ID пользователя: ${metadata?.userId})`,
        metadata,
      );
    }

    return provider.createPayment(price, currency, description +` (ID пользователя: ${metadata?.userId})`, metadata);
  }

  async onSubscriptionPaid(body: any) {
    // Stripe
    if (body?.type === 'checkout.session.completed' || body?.type === 'invoice.payment_succeeded') {
      const dataObj = body?.data?.object;
      const metadata = dataObj?.subscription_details?.metadata || dataObj?.metadata || dataObj?.payment_intent?.metadata;

      const userId = Number(metadata.userId);
      const user = await User.findOne({ where: { id: userId }, relations: ['subscriptions', 'libraryPurchases', 'libraryFavourites'] });

      if(metadata?.module == 'libraryPurchase') {
        const library= await LibraryTranslation.findOneBy({id: metadata?.libraryId});
        user.libraryPurchases.push(library);
        user.libraryFavourites.push(library);
        return await user.save();
      }

      if (!user || metadata?.module !== 'subscriptions') return { status: 'error' };

      const isYearly = metadata?.isYearly === 'true';

      const subType = metadata.value;
      let record = user.subscriptions.find(s => s.type === subType);

      if (!record) {
        record = new UserSubscriptions();
        record.type = subType;
        record.paymentId = dataObj?.id;
        record.isYearly = isYearly;
        user.subscriptions.push(record);
      }

      record.paymentId = dataObj?.id;
      record.provider = 'stripe';
      record.isAutoRenew = true;

      let cardLast4 = null;
      try {
        const paymentIntentId = dataObj?.payment_intent;
        if (paymentIntentId) {
          const paymentIntent = await this.stripeService.getPaymentIntent(paymentIntentId);

          if (paymentIntent?.payment_method) {
            const paymentMethodId = typeof paymentIntent.payment_method === 'string'
              ? paymentIntent.payment_method
              : paymentIntent.payment_method.id;
            const paymentMethod = await this.stripeService.getPaymentMethod(paymentMethodId);
            cardLast4 = paymentMethod?.card?.last4;
          }
        }
      } catch (error) {
      }

      if (cardLast4) {
        record.cardNumber = cardLast4;
      }

      if (body?.type === 'invoice.payment_succeeded' && dataObj?.subscription) {
        record.stripeSubscriptionId = dataObj?.subscription;
        record.paymentId = dataObj?.payment_intent || dataObj?.id;
      }

      const periodDays = isYearly ? 365 : 30;
      record.isYearly = isYearly;
      record.currentPeriodEnd = new Date(Date.now() + periodDays * 24 * 60 * 60 * 1000);

      await record.save();
      return await user.save();
    }

    // ЮKassa
    if (body?.event === 'payment.succeeded') {
      const dataObj = body?.object || body?.data?.object;
      const metadata = dataObj?.metadata;

      const userId = Number(metadata.userId);
      const user = await User.findOne({ where: { id: userId }, relations: ['subscriptions', 'libraryPurchases', 'libraryFavourites'] });

      if(metadata?.module == 'libraryPurchase') {
        const library= await LibraryTranslation.findOneBy({id: metadata?.libraryId});
        user.libraryPurchases.push(library);
        user.libraryFavourites.push(library);
        return await user.save();
      }

      if (!user || metadata?.module !== 'subscriptions') return { status: 'error' };

      const paymentId = dataObj?.id;
      if (await UserSubscriptions.findOneBy({ paymentId })) {
        return { status: 'ok' };
      }


      const subType = metadata.value;

      const newSubscription = new UserSubscriptions();
      newSubscription.type = subType;
      newSubscription.paymentId = paymentId;
      newSubscription.provider = 'yookassa';
      newSubscription.isAutoRenew = true;
      const isYearly = metadata?.isYearly || false;
      const periodDays = isYearly ? 365 : 30;
      newSubscription.isYearly = isYearly;
      newSubscription.currentPeriodEnd = new Date(Date.now() + periodDays * 24 * 60 * 60 * 1000);

      const pmId = dataObj?.payment_method?.id;
      if (newSubscription.isAutoRenew && pmId) {
        newSubscription.yookassaPaymentMethodId = pmId;
      }

      const cardLast4 = dataObj?.payment_method?.card?.last4;
      if (cardLast4) {
        newSubscription.cardNumber = cardLast4;
      }

      await newSubscription.save();
      user.subscriptions.push(newSubscription);
      return await user.save();
    }

    return { status: 'ignored' };
  }

  async cancelAutoRenew(subscriptionId: number, user: User) {
    const record = await UserSubscriptions.findOne({ where: { id: subscriptionId, user: { id: user.id } } });
    if (!record) throw new BadRequestException('Подписка не найдена');

    record.isAutoRenew = false;

    if (record.provider === 'stripe' && record.stripeSubscriptionId) {
      const stripe = this.providers.get(PaymentProviderType.STRIPE)
      await stripe.cancelAutoRenew(record.stripeSubscriptionId)
    }

    if (record.provider === 'yookassa' && record.yookassaPaymentMethodId) {
      const yookassa = this.providers.get(PaymentProviderType.YOOKASSA)
      await yookassa.cancelAutoRenew(record.yookassaPaymentMethodId)
      record.yookassaPaymentMethodId = null;
    }

    await record.save();
    return { success: true };
  }

  async getPaymentMethod(paymentMethodId: string) {
    return await this.stripeService.getPaymentMethod(paymentMethodId);
  }

}
